import { GoogleAuth } from 'google-auth-library';
import fs from 'fs';
import path from 'path';

export interface ServiceAccountKey {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

interface CachedToken {
  token: string;
  expiresAt: number;
}

export class GoogleCloudAuth {
  private auth: GoogleAuth;
  private serviceAccountPath: string;
  private cachedToken: CachedToken | null = null;
  private readonly TOKEN_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes buffer before expiry

  constructor() {
    // Look for account.json in root directory or env
    this.serviceAccountPath = this.findServiceAccountFile();

    this.auth = new GoogleAuth({
      keyFile: this.serviceAccountPath,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    });
  }

  private findServiceAccountFile(): string {
    const possiblePaths = [
      path.join(process.cwd(), 'account.json'),
      path.join(process.cwd(), 'env', 'account.json'),
      path.join(process.cwd(), '.env', 'account.json'),
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        return filePath;
      }
    }

    throw new Error('Service account file (account.json) not found. Please place it in the root directory or env folder.');
  }

  async getAccessToken(): Promise<string> {
    try {
      // Check if we have a valid cached token
      if (this.cachedToken && this.isTokenValid(this.cachedToken)) {
        console.log('Using cached access token');
        return this.cachedToken.token;
      }

      console.log('Fetching new access token');
      const client = await this.auth.getClient();
      const accessToken = await client.getAccessToken();

      if (!accessToken.token) {
        throw new Error('Failed to obtain access token');
      }

      // Cache the token with expiry time
      // Google Cloud tokens typically expire in 1 hour (3600 seconds)
      const expiresAt = Date.now() + (3600 * 1000) - this.TOKEN_BUFFER_TIME;
      this.cachedToken = {
        token: accessToken.token,
        expiresAt
      };

      return accessToken.token;
    } catch (error) {
      console.error('Error getting access token:', error);
      // Clear cached token on error
      this.cachedToken = null;
      throw error;
    }
  }

  private isTokenValid(cachedToken: CachedToken): boolean {
    return Date.now() < cachedToken.expiresAt;
  }

  public clearTokenCache(): void {
    this.cachedToken = null;
  }

  async getProjectId(): Promise<string> {
    try {
      return await this.auth.getProjectId();
    } catch (error) {
      console.error('Error getting project ID:', error);
      throw error;
    }
  }
}

