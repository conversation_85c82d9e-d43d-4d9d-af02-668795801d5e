import { apiCache, sessionCache } from './cache';

export interface ApiCacheConfig {
  ttl?: number;
  useCache?: boolean;
  invalidateOnMutation?: boolean;
}

export class ApiCacheManager {
  private static instance: ApiCacheManager;
  private readonly defaultConfig: ApiCacheConfig = {
    ttl: 2 * 60 * 1000, // 2 minutes
    useCache: true,
    invalidateOnMutation: true
  };

  static getInstance(): ApiCacheManager {
    if (!ApiCacheManager.instance) {
      ApiCacheManager.instance = new ApiCacheManager();
    }
    return ApiCacheManager.instance;
  }

  /**
   * Generate cache key for API requests
   */
  private generateCacheKey(url: string, method: string, body?: any): string {
    const bodyHash = body ? JSON.stringify(body) : '';
    return `${method}_${url}_${btoa(bodyHash).slice(0, 10)}`;
  }

  /**
   * Cached fetch wrapper
   */
  async cachedFetch(
    url: string,
    options: RequestInit & { cacheConfig?: ApiCacheConfig } = {}
  ): Promise<Response> {
    const { cacheConfig, ...fetchOptions } = options;
    const config = { ...this.defaultConfig, ...cacheConfig };
    const method = fetchOptions.method || 'GET';
    const cacheKey = this.generateCacheKey(url, method, fetchOptions.body);

    // Only cache GET requests and when caching is enabled
    if (method === 'GET' && config.useCache) {
      const cached = apiCache.get<{ status: number; data: any; headers: Record<string, string> }>(cacheKey);
      if (cached) {
        console.log(`Cache hit for ${method} ${url}`);
        return new Response(JSON.stringify(cached.data), {
          status: cached.status,
          headers: cached.headers
        });
      }
    }

    // Make the actual request
    console.log(`Cache miss for ${method} ${url}`);
    const response = await fetch(url, fetchOptions);
    
    // Cache successful GET responses
    if (method === 'GET' && config.useCache && response.ok) {
      try {
        const clonedResponse = response.clone();
        const data = await clonedResponse.json();
        const headers: Record<string, string> = {};
        
        // Store relevant headers
        clonedResponse.headers.forEach((value, key) => {
          headers[key] = value;
        });

        apiCache.set(cacheKey, {
          status: response.status,
          data,
          headers
        }, config.ttl);
      } catch (error) {
        console.warn('Failed to cache response:', error);
      }
    }

    // Invalidate related cache entries for mutations
    if (config.invalidateOnMutation && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      this.invalidateRelatedCache(url);
    }

    return response;
  }

  /**
   * Invalidate cache entries related to a URL
   */
  private invalidateRelatedCache(url: string): void {
    // For session-related endpoints, clear session cache
    if (url.includes('/api/sessions')) {
      console.log('Invalidating session cache due to mutation');
      this.invalidateSessionCache();
    }
  }

  /**
   * Invalidate all session-related cache
   */
  invalidateSessionCache(): void {
    // Clear API cache entries related to sessions
    apiCache.clear();
    
    // Clear session data cache
    sessionCache.clear();
  }

  /**
   * Invalidate specific cache entry
   */
  invalidateCache(url: string, method: string = 'GET', body?: any): void {
    const cacheKey = this.generateCacheKey(url, method, body);
    apiCache.remove(cacheKey);
  }

  /**
   * Clear all API cache
   */
  clearAllCache(): void {
    apiCache.clear();
  }
}

// Singleton instance
export const apiCacheManager = ApiCacheManager.getInstance();

/**
 * Enhanced fetch function with caching
 */
export async function cachedFetch(
  url: string, 
  options: RequestInit & { cacheConfig?: ApiCacheConfig } = {}
): Promise<Response> {
  return apiCacheManager.cachedFetch(url, options);
}

/**
 * Session-specific cache utilities
 */
export class SessionCacheUtils {
  static getCacheKey(userId: string, action: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : '';
    // Use a more reliable hash that includes the full parameter string
    const hash = btoa(paramStr).replace(/[^a-zA-Z0-9]/g, '').slice(0, 16);
    return `${userId}_${action}_${hash}_${paramStr ? btoa(JSON.stringify(params)).slice(-8) : ''}`;
  }

  static cacheSessionList(userId: string, sessions: any[], ttl?: number): void {
    const key = this.getCacheKey(userId, 'list');
    sessionCache.set(key, sessions, ttl);
  }

  static getCachedSessionList(userId: string): any[] | null {
    const key = this.getCacheKey(userId, 'list');
    return sessionCache.get(key);
  }

  static cacheSession(userId: string, sessionId: string, sessionData: any, ttl?: number): void {
    const key = this.getCacheKey(userId, 'get', { sessionId });
    sessionCache.set(key, sessionData, ttl);
  }

  static getCachedSession(userId: string, sessionId: string): any | null {
    const key = this.getCacheKey(userId, 'get', { sessionId });
    return sessionCache.get(key);
  }

  static invalidateUserSessions(userId: string): void {
    // This is a simplified approach - in a real implementation,
    // you might want to track keys more precisely
    sessionCache.clear();
  }

  static cacheSessionMessages(userId: string, sessionId: string, messages: any[], ttl?: number): void {
    const key = this.getCacheKey(userId, 'messages', { sessionId });
    sessionCache.set(key, messages, ttl);
  }

  static getCachedSessionMessages(userId: string, sessionId: string): any[] | null {
    const key = this.getCacheKey(userId, 'messages', { sessionId });
    const cached = sessionCache.get(key);
    console.log('Cache lookup for messages:', { userId, sessionId, key, found: !!cached, count: cached?.length || 0 });
    return cached;
  }

  static clearSessionMessages(userId: string, sessionId: string): void {
    const key = this.getCacheKey(userId, 'messages', { sessionId });
    sessionCache.remove(key);
    console.log('Cleared cached messages for session:', sessionId);
  }
}
