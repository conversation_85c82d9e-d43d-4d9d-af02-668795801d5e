@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom styles for markdown messages */
.message-content .prose {
  color: inherit;
}

.message-content .prose-invert {
  color: inherit;
}

.message-content .prose strong,
.message-content .prose-invert strong {
  color: inherit;
  font-weight: 600;
}

.message-content .prose em,
.message-content .prose-invert em {
  color: inherit;
}

.message-content .prose code,
.message-content .prose-invert code {
  background-color: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.message-content .prose-invert code {
  background-color: rgba(255, 255, 255, 0.2);
}

.message-content .prose pre,
.message-content .prose-invert pre {
  background-color: rgba(0, 0, 0, 0.05);
  color: inherit;
  margin: 0.5rem 0;
}

.message-content .prose-invert pre {
  background-color: rgba(255, 255, 255, 0.1);
}

.message-content .prose p:last-child,
.message-content .prose-invert p:last-child {
  margin-bottom: 0;
}
