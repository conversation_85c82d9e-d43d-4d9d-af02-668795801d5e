'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownMessageProps {
  content: string;
  className?: string;
}

export default function MarkdownMessage({ content, className = '' }: MarkdownMessageProps) {
  return (
    <ReactMarkdown
      className={`prose prose-sm max-w-none ${className}`}
      remarkPlugins={[remarkGfm]}
      components={{
        // Customize paragraph styling
        p: ({ children }) => (
          <p className="mb-2 last:mb-0 leading-relaxed">{children}</p>
        ),
        // Customize strong/bold text
        strong: ({ children }) => (
          <strong className="font-semibold text-current">{children}</strong>
        ),
        // Customize emphasis/italic text
        em: ({ children }) => (
          <em className="italic text-current">{children}</em>
        ),
        // Customize code blocks
        code: ({ inline, children }) => {
          if (inline) {
            return (
              <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono">
                {children}
              </code>
            );
          }
          return (
            <code className="block bg-gray-100 text-gray-800 p-2 rounded text-sm font-mono whitespace-pre-wrap overflow-x-auto">
              {children}
            </code>
          );
        },
        // Customize pre blocks (for code blocks)
        pre: ({ children }) => (
          <pre className="bg-gray-100 text-gray-800 p-3 rounded-lg text-sm font-mono overflow-x-auto my-2">
            {children}
          </pre>
        ),
        // Customize lists
        ul: ({ children }) => (
          <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
        ),
        li: ({ children }) => (
          <li className="leading-relaxed">{children}</li>
        ),
        // Customize headings
        h1: ({ children }) => (
          <h1 className="text-lg font-bold mb-2 text-current">{children}</h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-base font-bold mb-2 text-current">{children}</h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-sm font-bold mb-1 text-current">{children}</h3>
        ),
        // Customize blockquotes
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-gray-300 pl-3 italic my-2 text-current">
            {children}
          </blockquote>
        ),
        // Customize links
        a: ({ href, children }) => (
          <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            {children}
          </a>
        ),
        // Customize horizontal rules
        hr: () => <hr className="border-gray-300 my-3" />,
        // Customize tables
        table: ({ children }) => (
          <table className="border-collapse border border-gray-300 my-2 text-sm">
            {children}
          </table>
        ),
        th: ({ children }) => (
          <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-semibold text-left">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="border border-gray-300 px-2 py-1">{children}</td>
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
}
