/**
 * Request deduplication manager to prevent duplicate API calls
 */
export class RequestDeduplicationManager {
  private static instance: RequestDeduplicationManager;
  private pendingRequests = new Map<string, Promise<any>>();
  private requestTimeouts = new Map<string, NodeJS.Timeout>();
  private readonly defaultTimeout = 30000; // 30 seconds

  static getInstance(): RequestDeduplicationManager {
    if (!RequestDeduplicationManager.instance) {
      RequestDeduplicationManager.instance = new RequestDeduplicationManager();
    }
    return RequestDeduplicationManager.instance;
  }

  /**
   * Generate a unique key for the request
   */
  private generateRequestKey(url: string, method: string, body?: any): string {
    const bodyStr = body ? (typeof body === 'string' ? body : JSON.stringify(body)) : '';
    return `${method.toUpperCase()}_${url}_${btoa(bodyStr).slice(0, 16)}`;
  }

  /**
   * Execute request with deduplication
   */
  async deduplicatedRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    timeout: number = this.defaultTimeout
  ): Promise<T> {
    // Check if request is already pending
    if (this.pendingRequests.has(key)) {
      console.log(`Deduplicating request: ${key}`);
      return this.pendingRequests.get(key) as Promise<T>;
    }

    // Create new request promise
    const requestPromise = this.createRequestPromise(key, requestFn, timeout);
    this.pendingRequests.set(key, requestPromise);

    return requestPromise;
  }

  /**
   * Create request promise with timeout and cleanup
   */
  private async createRequestPromise<T>(
    key: string,
    requestFn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    // Set timeout for cleanup
    const timeoutId = setTimeout(() => {
      this.cleanup(key);
    }, timeout);
    
    this.requestTimeouts.set(key, timeoutId);

    try {
      const result = await requestFn();
      this.cleanup(key);
      return result;
    } catch (error) {
      this.cleanup(key);
      throw error;
    }
  }

  /**
   * Clean up request tracking
   */
  private cleanup(key: string): void {
    this.pendingRequests.delete(key);
    
    const timeoutId = this.requestTimeouts.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.requestTimeouts.delete(key);
    }
  }

  /**
   * Cancel a pending request
   */
  cancelRequest(key: string): void {
    this.cleanup(key);
  }

  /**
   * Cancel all pending requests
   */
  cancelAllRequests(): void {
    for (const key of this.pendingRequests.keys()) {
      this.cleanup(key);
    }
  }

  /**
   * Get number of pending requests
   */
  getPendingRequestCount(): number {
    return this.pendingRequests.size;
  }

  /**
   * Check if a request is pending
   */
  isRequestPending(key: string): boolean {
    return this.pendingRequests.has(key);
  }
}

// Singleton instance
export const requestDeduplicator = RequestDeduplicationManager.getInstance();

/**
 * Higher-order function to wrap API calls with deduplication
 */
export function withDeduplication<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  keyGenerator?: (...args: T) => string
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    return requestDeduplicator.deduplicatedRequest(
      key,
      () => fn(...args)
    );
  };
}

/**
 * Utility for creating deduplicated fetch requests
 */
export async function deduplicatedFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const method = options.method || 'GET';
  const key = requestDeduplicator['generateRequestKey'](url, method, options.body);
  
  return requestDeduplicator.deduplicatedRequest(
    key,
    () => fetch(url, options)
  );
}

/**
 * Session-specific deduplication utilities
 */
export class SessionRequestDeduplicator {
  static async listSessions(userId: string, fetchFn: () => Promise<any>): Promise<any> {
    const key = `list_sessions_${userId}`;
    return requestDeduplicator.deduplicatedRequest(key, fetchFn);
  }

  static async getSession(userId: string, sessionId: string, fetchFn: () => Promise<any>): Promise<any> {
    const key = `get_session_${userId}_${sessionId}`;
    return requestDeduplicator.deduplicatedRequest(key, fetchFn);
  }

  static async createSession(userId: string, fetchFn: () => Promise<any>): Promise<any> {
    const key = `create_session_${userId}_${Date.now()}`;
    return requestDeduplicator.deduplicatedRequest(key, fetchFn);
  }

  static async deleteSession(userId: string, sessionId: string, fetchFn: () => Promise<any>): Promise<any> {
    const key = `delete_session_${userId}_${sessionId}`;
    return requestDeduplicator.deduplicatedRequest(key, fetchFn);
  }
}

/**
 * React hook for managing request deduplication state
 */
export function useRequestDeduplication() {
  const getPendingCount = () => requestDeduplicator.getPendingRequestCount();
  const cancelAll = () => requestDeduplicator.cancelAllRequests();
  const isRequestPending = (key: string) => requestDeduplicator.isRequestPending(key);

  return {
    getPendingCount,
    cancelAll,
    isRequestPending
  };
}
