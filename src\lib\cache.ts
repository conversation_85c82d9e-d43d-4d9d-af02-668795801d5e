export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

export interface CacheOptions {
  ttl?: number; // Default TTL in milliseconds
  useLocalStorage?: boolean;
  keyPrefix?: string;
}

export class CacheManager {
  private memoryCache = new Map<string, CacheItem<any>>();
  private readonly defaultTTL: number;
  private readonly useLocalStorage: boolean;
  private readonly keyPrefix: string;
  private readonly pendingRequests = new Map<string, Promise<any>>();

  constructor(options: CacheOptions = {}) {
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
    this.useLocalStorage = options.useLocalStorage ?? true;
    this.keyPrefix = options.keyPrefix || 'cache_';
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const fullKey = this.keyPrefix + key;
    
    // Check memory cache first
    const memoryItem = this.memoryCache.get(fullKey);
    if (memoryItem && this.isValid(memoryItem)) {
      return memoryItem.data;
    }

    // Check localStorage if enabled
    if (this.useLocalStorage && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(fullKey);
        if (stored) {
          const item: CacheItem<T> = JSON.parse(stored);
          if (this.isValid(item)) {
            // Restore to memory cache
            this.memoryCache.set(fullKey, item);
            return item.data;
          } else {
            // Remove expired item
            localStorage.removeItem(fullKey);
          }
        }
      } catch (error) {
        console.warn('Error reading from localStorage:', error);
      }
    }

    return null;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const fullKey = this.keyPrefix + key;
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    };

    // Store in memory
    this.memoryCache.set(fullKey, item);

    // Store in localStorage if enabled
    if (this.useLocalStorage && typeof window !== 'undefined') {
      try {
        localStorage.setItem(fullKey, JSON.stringify(item));
      } catch (error) {
        console.warn('Error writing to localStorage:', error);
      }
    }
  }

  /**
   * Remove item from cache
   */
  remove(key: string): void {
    const fullKey = this.keyPrefix + key;
    
    this.memoryCache.delete(fullKey);
    
    if (this.useLocalStorage && typeof window !== 'undefined') {
      try {
        localStorage.removeItem(fullKey);
      } catch (error) {
        console.warn('Error removing from localStorage:', error);
      }
    }
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.memoryCache.clear();
    
    if (this.useLocalStorage && typeof window !== 'undefined') {
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith(this.keyPrefix)) {
            localStorage.removeItem(key);
          }
        });
      } catch (error) {
        console.warn('Error clearing localStorage:', error);
      }
    }
  }

  /**
   * Get data with fallback function and caching
   */
  async getOrFetch<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    // Check cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Check if request is already pending to avoid duplicate calls
    const pendingKey = this.keyPrefix + key + '_pending';
    if (this.pendingRequests.has(pendingKey)) {
      return this.pendingRequests.get(pendingKey);
    }

    // Create and store the pending request
    const promise = fetchFn().then(data => {
      this.set(key, data, ttl);
      this.pendingRequests.delete(pendingKey);
      return data;
    }).catch(error => {
      this.pendingRequests.delete(pendingKey);
      throw error;
    });

    this.pendingRequests.set(pendingKey, promise);
    return promise;
  }

  /**
   * Check if cache item is still valid
   */
  private isValid<T>(item: CacheItem<T>): boolean {
    return Date.now() - item.timestamp < item.ttl;
  }

  /**
   * Clean up expired items
   */
  cleanup(): void {
    // Clean memory cache
    for (const [key, item] of this.memoryCache.entries()) {
      if (!this.isValid(item)) {
        this.memoryCache.delete(key);
      }
    }

    // Clean localStorage
    if (this.useLocalStorage && typeof window !== 'undefined') {
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith(this.keyPrefix)) {
            try {
              const stored = localStorage.getItem(key);
              if (stored) {
                const item = JSON.parse(stored);
                if (!this.isValid(item)) {
                  localStorage.removeItem(key);
                }
              }
            } catch (error) {
              // Remove corrupted items
              localStorage.removeItem(key);
            }
          }
        });
      } catch (error) {
        console.warn('Error during localStorage cleanup:', error);
      }
    }
  }
}

// Create singleton instances for different types of data
export const sessionCache = new CacheManager({
  ttl: 10 * 60 * 1000, // 10 minutes for session data
  keyPrefix: 'session_',
  useLocalStorage: true
});

export const apiCache = new CacheManager({
  ttl: 2 * 60 * 1000, // 2 minutes for API responses
  keyPrefix: 'api_',
  useLocalStorage: false // API responses in memory only
});

export const userDataCache = new CacheManager({
  ttl: 30 * 60 * 1000, // 30 minutes for user data
  keyPrefix: 'user_',
  useLocalStorage: true
});

// Auto cleanup every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    sessionCache.cleanup();
    apiCache.cleanup();
    userDataCache.cleanup();
  }, 5 * 60 * 1000);
}
