import axios, { AxiosResponse } from 'axios';
import { GoogleCloudAuth } from './auth';
import { SessionCacheUtils } from './api-cache';
import { SessionRequestDeduplicator } from './request-deduplication';

export interface Session {
  name: string;
  createTime: string;
  updateTime: string;
  ttl?: string;
}

export interface StreamQueryRequest {
  user_id: string;
  session_id?: string;
  message: string;
}

export interface CreateSessionRequest {
  user_id: string;
  ttl?: string;
}

export interface SessionOperationRequest {
  user_id: string;
  session_id: string;
}

export interface ListSessionsRequest {
  user_id: string;
  page_size?: number;
  page_token?: string;
}

export class VertexAIClient {
  private auth: GoogleCloudAuth;
  private baseUrl: string;
  private streamUrl: string;
  private projectId: string = 'adk-gp';
  private location: string = 'us-central1';
  private reasoningEngineId: string = '1740192655234564096';

  constructor() {
    this.auth = new GoogleCloudAuth();
    // this.baseUrl = `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/reasoningEngines/${this.reasoningEngineId}:query`;
    // this.streamUrl = `https://${this.location}-aiplatform.googleapis.com/v1/projects/${this.projectId}/locations/${this.location}/reasoningEngines/${this.reasoningEngineId}:streamQuery`;
    this.baseUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/adk-gp/locations/us-central1/reasoningEngines/1740192655234564096:query`;
    this.streamUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/adk-gp/locations/us-central1/reasoningEngines/1740192655234564096:streamQuery`;
  }

  private async getAuthHeaders(): Promise<{ [key: string]: string }> {
    const accessToken = await this.auth.getAccessToken();
    return {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  async createSession(request: CreateSessionRequest): Promise<Session> {
    // Use deduplication for session creation
    return SessionRequestDeduplicator.createSession(request.user_id, async () => {
      try {
        const headers = await this.getAuthHeaders();
        const response: AxiosResponse<Session> = await axios.post(
          this.baseUrl,
          {
            class_method: 'create_session',
            input: {
              user_id: request.user_id,
              ttl: request.ttl,
            },
          },
          { headers }
        );

        // Invalidate session list cache after creating new session
        SessionCacheUtils.invalidateUserSessions(request.user_id);

        return response.data;
      } catch (error) {
        console.error('Error creating session:', error);
        throw error;
      }
    });
  }

  async listSessions(request: ListSessionsRequest): Promise<{ sessions: Session[]; nextPageToken?: string }> {
    // Check cache first (only for first page without page_token)
    if (!request.page_token) {
      const cached = SessionCacheUtils.getCachedSessionList(request.user_id);
      if (cached) {
        console.log('Using cached session list for user:', request.user_id);
        return { sessions: cached };
      }
    }

    // Use deduplication for the API call
    return SessionRequestDeduplicator.listSessions(request.user_id, async () => {
      try {
        const headers = await this.getAuthHeaders();
        const response = await axios.post(
          this.baseUrl,
          {
            class_method: 'list_sessions',
            input: {
              user_id: request.user_id,
              page_size: request.page_size,
              page_token: request.page_token,
            },
          },
          { headers }
        );

        console.log('VertexAI listSessions response:', JSON.stringify(response.data, null, 2));

        // Cache the result (only for first page)
        if (!request.page_token && response.data?.output?.sessions) {
          SessionCacheUtils.cacheSessionList(
            request.user_id,
            response.data.output.sessions,
            5 * 60 * 1000 // 5 minutes TTL
          );
        }

        return response.data;
      } catch (error: any) {
        console.error('Error listing sessions:', JSON.stringify(error, null, 2));
        throw error;
      }
    });
  }

  async getSession(request: SessionOperationRequest): Promise<Session> {
    // Check cache first
    const cached = SessionCacheUtils.getCachedSession(request.user_id, request.session_id);
    if (cached) {
      console.log('Using cached session data for:', request.session_id);
      return cached;
    }

    // Use deduplication for the API call
    return SessionRequestDeduplicator.getSession(request.user_id, request.session_id, async () => {
      try {
        const headers = await this.getAuthHeaders();
        const response: AxiosResponse<Session> = await axios.post(
          this.baseUrl,
          {
            class_method: 'get_session',
            input: {
              user_id: request.user_id,
              session_id: request.session_id,
            },
          },
          { headers }
        );

        // Cache the result
        SessionCacheUtils.cacheSession(
          request.user_id,
          request.session_id,
          response.data,
          10 * 60 * 1000 // 10 minutes TTL
        );

        return response.data;
      } catch (error) {
        console.error('Error getting session:', error);
        throw error;
      }
    });
  }

  async deleteSession(request: SessionOperationRequest): Promise<void> {
    // Use deduplication for session deletion
    return SessionRequestDeduplicator.deleteSession(request.user_id, request.session_id, async () => {
      try {
        const headers = await this.getAuthHeaders();
        await axios.post(
          this.baseUrl,
          {
            class_method: 'delete_session',
            input: {
              user_id: request.user_id,
              session_id: request.session_id,
            },
          },
          { headers }
        );

        // Invalidate related cache entries after deletion
        SessionCacheUtils.invalidateUserSessions(request.user_id);

      } catch (error) {
        console.error('Error deleting session:', error);
        throw error;
      }
    });
  }

  async *streamQuery(request: StreamQueryRequest): AsyncGenerator<unknown, void, unknown> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.post(
        this.streamUrl,
        {
          class_method: 'stream_query',
          input: {
            user_id: request.user_id,
            session_id: request.session_id,
            message: request.message,
          },
        },
        {
          headers,
          responseType: 'stream',
        }
      );

      const stream = response.data;
      let buffer = '';

      for await (const chunk of stream) {
        buffer += chunk.toString();
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              yield data;
            } catch {
              // Skip invalid JSON lines
              console.warn('Failed to parse streaming response line:', line);
            }
          }
        }
      }

      // Process any remaining buffer
      if (buffer.trim()) {
        try {
          const data = JSON.parse(buffer);
          yield data;
        } catch {
          console.warn('Failed to parse final buffer:', buffer);
        }
      }
    } catch (error) {
      console.error('Error streaming query:', error);
      throw error;
    }
  }
}

